2025-08-13 17:05:36,310 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 17:05:37,641 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 17:05:45,625 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:46] - === 开始数据库用户认证流程 ===
2025-08-13 17:05:45,626 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:47] - 请求认证的用户名: ad***n
2025-08-13 17:05:45,680 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:56] - ✅ 找到用户: ad***n (ID: ***)
2025-08-13 17:05:45,680 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:57] - 用户编码: ADMIN001
2025-08-13 17:05:45,680 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:58] - 真实姓名: 系**
2025-08-13 17:05:45,681 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:59] - OA姓名: 系**
2025-08-13 17:05:45,681 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:60] - 用户状态: 1
2025-08-13 17:05:45,681 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:64] - 密码哈希长度: 60
2025-08-13 17:05:45,681 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:65] - 密码哈希格式: $2a$10$FxZ
2025-08-13 17:05:45,687 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:147] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-13 17:05:45,689 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:124] - === DatabaseUserDetailsService处理完成 ===
2025-08-13 17:05:45,759 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-13 17:05:45,799 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:232] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-13 17:05:45,800 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:72] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-13 17:05:45,804 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:89] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-13 17:05:45,807 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:273] - JWT Refresh Token生成成功: username=admin
2025-08-13 17:06:32,063 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 17:06:33,380 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 17:06:46,389 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:46] - === 开始数据库用户认证流程 ===
2025-08-13 17:06:46,389 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:47] - 请求认证的用户名: ad***n
2025-08-13 17:06:46,437 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:56] - ✅ 找到用户: ad***n (ID: ***)
2025-08-13 17:06:46,437 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:57] - 用户编码: ADMIN001
2025-08-13 17:06:46,437 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:58] - 真实姓名: 系**
2025-08-13 17:06:46,437 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:59] - OA姓名: 系**
2025-08-13 17:06:46,437 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:60] - 用户状态: 1
2025-08-13 17:06:46,438 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:64] - 密码哈希长度: 60
2025-08-13 17:06:46,438 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:65] - 密码哈希格式: $2a$10$FxZ
2025-08-13 17:06:46,444 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:147] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-13 17:06:46,446 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:124] - === DatabaseUserDetailsService处理完成 ===
2025-08-13 17:06:46,514 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-13 17:06:46,555 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:232] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-13 17:06:46,556 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:72] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-13 17:06:46,560 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:89] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-13 17:06:46,563 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:273] - JWT Refresh Token生成成功: username=admin
2025-08-13 17:13:43,151 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 17:13:44,490 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 17:13:56,377 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:46] - === 开始数据库用户认证流程 ===
2025-08-13 17:13:56,378 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:47] - 请求认证的用户名: ad***n
2025-08-13 17:13:56,428 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:56] - ✅ 找到用户: ad***n (ID: ***)
2025-08-13 17:13:56,429 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:57] - 用户编码: ADMIN001
2025-08-13 17:13:56,429 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:58] - 真实姓名: 系**
2025-08-13 17:13:56,429 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:59] - OA姓名: 系**
2025-08-13 17:13:56,429 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:60] - 用户状态: 1
2025-08-13 17:13:56,429 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:64] - 密码哈希长度: 60
2025-08-13 17:13:56,429 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:65] - 密码哈希格式: $2a$10$FxZ
2025-08-13 17:13:56,436 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:147] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-13 17:13:56,438 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:124] - === DatabaseUserDetailsService处理完成 ===
2025-08-13 17:13:56,507 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-13 17:13:56,547 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755076436539
2025-08-13 17:13:56,549 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-13 17:13:56,551 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-13 17:13:56,556 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-13 17:13:56,558 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755076436539
2025-08-13 17:13:56,560 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-13 17:14:32,181 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 17:14:34,163 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
