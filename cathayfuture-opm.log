2025-08-13 11:09:38,386 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 45899 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 11:09:38,395 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 11:09:39,120 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 11:09:39,122 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 11:09:39,159 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 31ms. Found 0 repository interfaces.
2025-08-13 11:09:39,415 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ab1f7ccd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 11:09:39,551 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 11:09:39,558 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 11:09:39,563 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 11:09:39,564 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 11:09:39,622 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 11:09:39,622 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1205 ms
2025-08-13 11:09:40,330 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 11:09:40,442 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 11:09:40,442 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 11:09:40,961 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:88] - === 安全配置信息 ===
2025-08-13 11:09:40,961 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:89] - 开发模式: 启用 ⚠️
2025-08-13 11:09:40,961 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:90] - 数据库认证: 启用
2025-08-13 11:09:40,961 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:91] - 当前环境: [uat]
2025-08-13 11:09:40,962 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:98] - ===================
2025-08-13 11:09:41,026 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 45 ms to scan 5 urls, producing 127 keys and 456 values 
2025-08-13 11:09:41,198 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:151] - 配置数据库认证提供者
2025-08-13 11:09:41,205 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 11:09:41,205 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 11:09:41,205 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 11:09:41,242 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3a54638b, org.springframework.security.web.context.SecurityContextPersistenceFilter@56cd5d76, org.springframework.security.web.header.HeaderWriterFilter@25f7cc38, org.springframework.security.web.authentication.logout.LogoutFilter@627acb38, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@302da330, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5d9bb69b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7d8f2eec, org.springframework.security.web.session.SessionManagementFilter@102af1bb, org.springframework.security.web.access.ExceptionTranslationFilter@315cf170, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@97b5e6a]
2025-08-13 11:09:41,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 11:09:41,620 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 11:09:41,687 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 11:09:41,694 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 11:09:41,695 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.63 seconds (JVM running for 3.956)
2025-08-13 11:09:42,139 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-13 11:09:42,147 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 11:09:42,148 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-13 11:09:42,154 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 6 ms
2025-08-13 11:09:42,284 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-13 11:11:01,968 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 11:11:01,991 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-13 11:11:01,994 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-13 11:17:25,926 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 47421 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 11:17:25,928 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 11:17:26,626 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 11:17:26,627 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 11:17:26,664 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 31ms. Found 0 repository interfaces.
2025-08-13 11:17:26,908 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$1183fd3f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 11:17:27,027 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 11:17:27,035 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 11:17:27,040 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 11:17:27,040 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 11:17:27,093 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 11:17:27,093 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1146 ms
2025-08-13 11:17:27,806 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 11:17:27,918 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 11:17:27,918 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 11:17:28,446 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:88] - === 安全配置信息 ===
2025-08-13 11:17:28,446 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:89] - 开发模式: 启用 ⚠️
2025-08-13 11:17:28,446 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:90] - 数据库认证: 启用
2025-08-13 11:17:28,446 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:91] - 当前环境: [uat]
2025-08-13 11:17:28,447 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:98] - ===================
2025-08-13 11:17:28,504 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 41 ms to scan 5 urls, producing 127 keys and 456 values 
2025-08-13 11:17:28,669 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:151] - 配置数据库认证提供者
2025-08-13 11:17:28,676 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 11:17:28,676 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 11:17:28,676 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 11:17:28,714 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@121cf6f4, org.springframework.security.web.context.SecurityContextPersistenceFilter@9bf63d2, org.springframework.security.web.header.HeaderWriterFilter@77cd235b, org.springframework.security.web.authentication.logout.LogoutFilter@3a8fb023, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7f53fc38, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@18811c42, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7cdc4070, org.springframework.security.web.session.SessionManagementFilter@5d9bb69b, org.springframework.security.web.access.ExceptionTranslationFilter@797f97e3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1d585fb]
2025-08-13 11:17:28,806 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 11:17:29,075 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 11:17:29,141 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 11:17:29,148 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 11:17:29,150 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.55 seconds (JVM running for 3.896)
2025-08-13 11:17:29,623 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-13 11:17:29,626 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 11:17:29,626 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-13 11:17:29,633 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 7 ms
2025-08-13 11:17:29,732 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-13 11:17:33,519 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 11:17:33,544 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-13 11:17:33,547 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-13 12:41:00,847 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 62819 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 12:41:00,849 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 12:41:01,497 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 12:41:01,498 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 12:41:01,536 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-13 12:41:01,803 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6ad6b989] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 12:41:01,934 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 12:41:01,942 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 12:41:01,946 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 12:41:01,947 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 12:41:02,002 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 12:41:02,002 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1133 ms
2025-08-13 12:41:02,710 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 12:41:02,823 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 12:41:02,823 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 12:41:03,329 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:88] - === 安全配置信息 ===
2025-08-13 12:41:03,329 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:89] - 开发模式: 启用 ⚠️
2025-08-13 12:41:03,329 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:90] - 数据库认证: 启用
2025-08-13 12:41:03,329 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:91] - 当前环境: [uat]
2025-08-13 12:41:03,330 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:98] - ===================
2025-08-13 12:41:03,391 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 43 ms to scan 5 urls, producing 127 keys and 456 values 
2025-08-13 12:41:03,555 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:151] - 配置数据库认证提供者
2025-08-13 12:41:03,561 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 12:41:03,562 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 12:41:03,562 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 12:41:03,600 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@30704f85, org.springframework.security.web.context.SecurityContextPersistenceFilter@54e0f76f, org.springframework.security.web.header.HeaderWriterFilter@204b0f07, org.springframework.security.web.authentication.logout.LogoutFilter@760a2b6e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7462ba4b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@35ab4260, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2b99c937, org.springframework.security.web.session.SessionManagementFilter@37cf91d8, org.springframework.security.web.access.ExceptionTranslationFilter@4a520f05, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@93ce24d]
2025-08-13 12:41:03,694 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 12:41:03,966 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 12:41:04,030 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 12:41:04,036 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 12:41:04,038 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.519 seconds (JVM running for 3.847)
2025-08-13 12:41:04,496 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 12:41:04,496 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-13 12:41:04,497 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-13 12:41:04,505 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 8 ms
2025-08-13 12:41:04,628 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-13 12:41:22,421 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 12:41:22,461 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-13 12:41:22,465 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-13 14:44:59,260 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 77601 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 14:44:59,263 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 14:44:59,936 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 14:44:59,937 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 14:44:59,976 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 33ms. Found 0 repository interfaces.
2025-08-13 14:45:00,243 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$24352bd3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 14:45:00,431 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 14:45:00,440 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 14:45:00,446 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 14:45:00,446 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 14:45:00,509 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 14:45:00,509 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1226 ms
2025-08-13 14:45:01,231 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 14:45:01,344 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 14:45:01,344 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 14:45:01,860 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:48] - === 开发环境安全配置 ===
2025-08-13 14:45:01,860 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:49] - 当前环境: [uat]
2025-08-13 14:45:01,860 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:50] - 认证模式: 开发模式（无需认证）⚠️
2025-08-13 14:45:01,860 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:51] - 数据库认证: 启用（仅用于登录接口）
2025-08-13 14:45:01,861 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:53] - =======================
2025-08-13 14:45:01,921 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 44 ms to scan 5 urls, producing 129 keys and 465 values 
2025-08-13 14:45:02,111 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:77] - 配置数据库认证提供者（开发环境）
2025-08-13 14:45:02,118 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 14:45:02,118 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 14:45:02,118 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 14:45:02,156 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1328f482, org.springframework.security.web.context.SecurityContextPersistenceFilter@391d28ea, org.springframework.security.web.header.HeaderWriterFilter@25d9291a, org.springframework.security.web.authentication.logout.LogoutFilter@72a34537, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4ab455e2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@51813065, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7b8b07ae, org.springframework.security.web.session.SessionManagementFilter@5ae66c98, org.springframework.security.web.access.ExceptionTranslationFilter@7b1c501d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@534d0e20]
2025-08-13 14:45:02,237 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 14:45:02,515 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 14:45:02,586 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 14:45:02,593 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 14:45:02,595 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.672 seconds (JVM running for 4.001)
2025-08-13 14:45:16,230 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 14:45:16,231 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-13 14:45:16,237 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 6 ms
2025-08-13 14:45:17,513 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] o.s.api.AbstractOpenApiResource [L:347] - Init duration for springdoc-openapi is: 732 ms
2025-08-13 14:45:29,387 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-13 14:45:29,494 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-13 14:45:37,452 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-3] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 14:45:37,476 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-3] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-13 14:45:37,480 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-3] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-13 15:56:58,081 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 89992 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 15:56:58,083 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 15:56:58,735 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 15:56:58,737 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 15:56:58,774 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-13 15:56:59,017 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$25cb2d66] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 15:56:59,174 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 15:56:59,182 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 15:56:59,187 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 15:56:59,187 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 15:56:59,241 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 15:56:59,241 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1139 ms
2025-08-13 15:56:59,335 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 15:56:59,348 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:01:00,709 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 90732 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:01:00,711 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:01:01,358 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:01:01,359 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:01:01,396 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-13 16:01:01,655 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$218a3006] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:01:01,802 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:01:01,811 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:01:01,816 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:01:01,816 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:01:01,873 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:01:01,874 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1144 ms
2025-08-13 16:01:01,968 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:01:01,982 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:01:48,414 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 90878 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:01:48,416 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:01:49,072 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:01:49,073 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:01:49,110 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 31ms. Found 0 repository interfaces.
2025-08-13 16:01:49,364 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$4f01784d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:01:49,506 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:01:49,514 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:01:49,519 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:01:49,519 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:01:49,571 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:01:49,571 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1137 ms
2025-08-13 16:01:49,666 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:01:49,678 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:06:26,964 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 92494 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:06:26,966 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:06:27,613 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:06:27,614 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:06:27,650 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 31ms. Found 0 repository interfaces.
2025-08-13 16:06:27,895 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$5afb2c2f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:06:28,033 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:06:28,041 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:06:28,046 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:06:28,046 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:06:28,100 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:06:28,100 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1115 ms
2025-08-13 16:06:28,196 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:06:28,208 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:09:01,330 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 93275 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:09:01,334 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:09:01,974 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:09:01,975 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:09:02,014 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 33ms. Found 0 repository interfaces.
2025-08-13 16:09:02,274 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6c0eb8ab] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:09:02,424 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:09:02,433 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:09:02,438 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:09:02,438 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:09:02,494 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:09:02,495 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1139 ms
2025-08-13 16:09:02,591 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:09:02,604 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:15:38,429 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 95119 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:15:38,431 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:16:14,463 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 95268 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:16:14,465 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:16:15,094 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:16:15,095 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:16:15,134 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 33ms. Found 0 repository interfaces.
2025-08-13 16:16:15,385 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ef6fddab] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:16:15,503 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:16:15,511 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:16:15,515 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:16:15,516 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:16:15,566 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:16:15,567 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1083 ms
2025-08-13 16:16:16,251 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 16:16:16,356 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:16:16,356 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:16:16,846 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:48] - === 开发环境安全配置 ===
2025-08-13 16:16:16,846 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:49] - 当前环境: [uat]
2025-08-13 16:16:16,846 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:50] - 认证模式: 开发模式（无需认证）⚠️
2025-08-13 16:16:16,846 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:51] - 数据库认证: 启用（仅用于登录接口）
2025-08-13 16:16:16,847 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:53] - =======================
2025-08-13 16:16:16,862 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:16:16,870 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:19:01,654 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 95511 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:19:01,656 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:19:02,302 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:19:02,303 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:19:02,341 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-13 16:19:02,591 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$46071d01] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:19:02,708 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:19:02,716 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:19:02,721 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:19:02,721 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:19:02,771 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:19:02,771 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1097 ms
2025-08-13 16:19:03,472 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 16:19:03,577 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:19:03,577 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:19:04,078 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:48] - === 开发环境安全配置 ===
2025-08-13 16:19:04,078 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:49] - 当前环境: [uat]
2025-08-13 16:19:04,078 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:50] - 认证模式: 开发模式（无需认证）⚠️
2025-08-13 16:19:04,078 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:51] - 数据库认证: 启用（仅用于登录接口）
2025-08-13 16:19:04,078 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:53] - =======================
2025-08-13 16:19:04,093 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:19:04,101 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:19:47,698 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 95574 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:19:47,699 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:19:48,352 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:19:48,353 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:19:48,391 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-13 16:19:48,652 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$2a283a92] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:19:48,775 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:19:48,783 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:19:48,788 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:19:48,788 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:19:48,841 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:19:48,841 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1123 ms
2025-08-13 16:19:49,534 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 16:19:49,651 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:19:49,651 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:19:50,155 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:48] - === 开发环境安全配置 ===
2025-08-13 16:19:50,155 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:49] - 当前环境: [uat]
2025-08-13 16:19:50,155 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:50] - 认证模式: 开发模式（无需认证）⚠️
2025-08-13 16:19:50,155 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:51] - 数据库认证: 启用（仅用于登录接口）
2025-08-13 16:19:50,155 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:53] - =======================
2025-08-13 16:19:50,170 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:19:50,179 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:22:24,272 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 95816 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:22:24,274 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:22:24,984 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:22:24,985 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:22:25,022 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-13 16:22:25,276 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$8f27e339] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:22:25,405 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:22:25,413 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:22:25,418 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:22:25,418 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:22:25,472 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:22:25,472 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1180 ms
2025-08-13 16:22:26,157 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 16:22:26,267 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:22:26,267 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:22:26,772 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:48] - === 开发环境安全配置 ===
2025-08-13 16:22:26,772 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:49] - 当前环境: [uat]
2025-08-13 16:22:26,772 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:50] - 认证模式: 开发模式（无需认证）⚠️
2025-08-13 16:22:26,772 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:51] - 数据库认证: 启用（仅用于登录接口）
2025-08-13 16:22:26,772 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:53] - =======================
2025-08-13 16:22:26,787 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:22:26,795 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:38:11,509 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 97730 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:38:11,511 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:38:12,161 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:38:12,162 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:38:12,200 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-13 16:38:12,452 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$4d2025f6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:38:12,602 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:38:12,610 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:38:12,615 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:38:12,616 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:38:12,669 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:38:12,669 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1138 ms
2025-08-13 16:38:12,763 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:38:12,776 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:48:34,561 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 98875 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:48:34,563 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:48:35,200 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:48:35,201 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:48:35,239 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 33ms. Found 0 repository interfaces.
2025-08-13 16:48:35,483 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$afb22388] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:48:35,597 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:48:35,605 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:48:35,610 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:48:35,610 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:48:35,661 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:48:35,661 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1080 ms
2025-08-13 16:48:36,345 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 16:48:36,456 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:48:36,456 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:48:36,960 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:48] - === 开发环境安全配置 ===
2025-08-13 16:48:36,961 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:49] - 当前环境: [uat]
2025-08-13 16:48:36,961 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:50] - 认证模式: 开发模式（无需认证）⚠️
2025-08-13 16:48:36,961 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:51] - 数据库认证: 启用（仅用于登录接口）
2025-08-13 16:48:36,962 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:53] - =======================
2025-08-13 16:48:37,023 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 43 ms to scan 5 urls, producing 130 keys and 470 values 
2025-08-13 16:48:37,088 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:99] - BMS JWT工具类初始化完成
2025-08-13 16:48:37,089 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:100] - Access Token有效期: 24 小时
2025-08-13 16:48:37,089 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:101] - Refresh Token有效期: 48 小时
2025-08-13 16:48:37,089 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:102] - 发行人: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 16:48:37,089 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:103] - 受众: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 16:48:37,183 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:77] - 配置数据库认证提供者（开发环境）
2025-08-13 16:48:37,191 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 16:48:37,191 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 16:48:37,191 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 16:48:37,236 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@19ec5357, org.springframework.security.web.context.SecurityContextPersistenceFilter@4ab455e2, org.springframework.security.web.header.HeaderWriterFilter@5d5574c7, org.springframework.security.web.authentication.logout.LogoutFilter@44ddb518, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7ac685ef, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@e0847a9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@351d93bd, org.springframework.security.web.session.SessionManagementFilter@391d28ea, org.springframework.security.web.access.ExceptionTranslationFilter@25d9291a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3f4a605f]
2025-08-13 16:48:37,326 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 16:48:37,598 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 16:48:37,663 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 16:48:37,669 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 16:48:37,671 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.429 seconds (JVM running for 3.716)
2025-08-13 16:48:38,266 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 16:48:38,266 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-13 16:48:38,269 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-13 16:48:38,278 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 12 ms
2025-08-13 16:48:38,403 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-13 16:48:43,934 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 16:48:43,966 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-13 16:48:43,970 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-13 16:48:48,597 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 98899 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:48:48,598 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:48:49,460 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:48:49,463 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:48:49,507 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 35ms. Found 0 repository interfaces.
2025-08-13 16:48:49,906 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b25719b9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:48:50,146 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:48:50,159 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:48:50,170 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:48:50,170 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:48:50,271 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:48:50,271 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1637 ms
2025-08-13 16:48:51,505 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 16:49:22,446 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 98946 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:49:22,448 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:49:23,103 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:49:23,104 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:49:23,142 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-13 16:49:23,387 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$bd9bea9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:49:23,508 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:49:23,516 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:49:23,522 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:49:23,522 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:49:23,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:49:23,582 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1115 ms
2025-08-13 16:49:24,336 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 16:49:24,441 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:49:24,441 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:49:24,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:48] - === 开发环境安全配置 ===
2025-08-13 16:49:24,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:49] - 当前环境: [uat]
2025-08-13 16:49:24,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:50] - 认证模式: 开发模式（无需认证）⚠️
2025-08-13 16:49:24,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:51] - 数据库认证: 启用（仅用于登录接口）
2025-08-13 16:49:24,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:53] - =======================
2025-08-13 16:49:25,009 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 46 ms to scan 5 urls, producing 130 keys and 470 values 
2025-08-13 16:49:25,079 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:99] - BMS JWT工具类初始化完成
2025-08-13 16:49:25,079 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:100] - Access Token有效期: 24 小时
2025-08-13 16:49:25,079 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:101] - Refresh Token有效期: 48 小时
2025-08-13 16:49:25,079 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:102] - 发行人: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 16:49:25,079 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:103] - 受众: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 16:49:25,184 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:77] - 配置数据库认证提供者（开发环境）
2025-08-13 16:49:25,191 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 16:49:25,192 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 16:49:25,192 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 16:49:25,233 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@794d28a3, org.springframework.security.web.context.SecurityContextPersistenceFilter@72a34537, org.springframework.security.web.header.HeaderWriterFilter@e0847a9, org.springframework.security.web.authentication.logout.LogoutFilter@22ae32ba, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@44ddb518, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@195498aa, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@a55e82a, org.springframework.security.web.session.SessionManagementFilter@28f90752, org.springframework.security.web.access.ExceptionTranslationFilter@51813065, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@135064ea]
2025-08-13 16:49:25,327 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 16:49:25,608 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 16:49:25,674 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 16:49:25,680 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 16:49:25,682 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.576 seconds (JVM running for 3.921)
2025-08-13 16:49:26,120 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-13 16:49:26,122 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 16:49:26,122 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-13 16:49:26,129 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 7 ms
2025-08-13 16:49:26,250 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-13 16:49:38,504 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 16:49:38,523 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-13 16:49:38,526 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-13 16:50:34,926 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 99135 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:50:34,928 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:50:35,579 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:50:35,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:50:35,616 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 31ms. Found 0 repository interfaces.
2025-08-13 16:50:35,863 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$9edc1122] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:50:35,982 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:50:35,990 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:50:35,995 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:50:35,995 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:50:36,046 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:50:36,046 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1100 ms
2025-08-13 16:50:36,738 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 16:50:36,846 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:50:36,846 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:50:37,258 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:50:37,266 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:51:16,050 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 99218 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-13 16:51:16,052 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:51:16,758 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:51:16,759 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:51:16,795 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 31ms. Found 0 repository interfaces.
2025-08-13 16:51:17,042 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$178e271] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:51:17,158 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:51:17,166 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:51:17,171 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:51:17,171 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:51:17,224 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:51:17,224 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1154 ms
2025-08-13 16:51:17,907 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 16:51:18,018 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:51:18,018 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 16:51:18,514 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:52] - === 生产环境安全配置 ===
2025-08-13 16:51:18,514 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:53] - 当前环境: [uat]
2025-08-13 16:51:18,514 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:54] - 数据库认证: 启用
2025-08-13 16:51:18,514 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:55] - JWT认证: 启用 ✅
2025-08-13 16:51:18,515 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:56] - =======================
2025-08-13 16:51:18,578 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 45 ms to scan 5 urls, producing 130 keys and 470 values 
2025-08-13 16:51:18,646 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:99] - BMS JWT工具类初始化完成
2025-08-13 16:51:18,646 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:100] - Access Token有效期: 24 小时
2025-08-13 16:51:18,646 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:101] - Refresh Token有效期: 48 小时
2025-08-13 16:51:18,646 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:102] - 发行人: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 16:51:18,646 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:103] - 受众: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 16:51:18,746 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:90] - 配置数据库认证提供者（生产环境）
2025-08-13 16:51:18,753 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 16:51:18,753 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 16:51:18,753 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 16:51:18,773 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:61] - ✅ 配置生产环境安全策略：启用JWT认证
2025-08-13 16:51:18,776 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:82] - ✅ 生产环境安全配置已生效
2025-08-13 16:51:18,790 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@40a28bda, org.springframework.security.web.context.SecurityContextPersistenceFilter@7c8f047a, org.springframework.security.web.header.HeaderWriterFilter@2df71be0, org.springframework.security.web.authentication.logout.LogoutFilter@563843f1, cathayfuture.opm.adapter.security.JwtAuthenticationFilter@3becc950, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1dbff71c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1218e12, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@592cb470, org.springframework.security.web.session.SessionManagementFilter@1c7843c3, org.springframework.security.web.access.ExceptionTranslationFilter@1da53f4f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1c8f71a7]
2025-08-13 16:51:18,877 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 16:51:19,151 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 16:51:19,219 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 16:51:19,225 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 16:51:19,227 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.491 seconds (JVM running for 3.781)
2025-08-13 16:51:19,702 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-13 16:51:19,708 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 16:51:19,708 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-13 16:51:19,716 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 8 ms
2025-08-13 16:51:19,818 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-13 16:51:49,915 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-13 16:51:49,916 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-13 16:51:49,917 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 8
2025-08-13 16:51:49,917 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: admin123
2025-08-13 16:51:50,041 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:71] - === 检查用户状态 ===
2025-08-13 16:51:50,041 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:73] - 用户是否启用: true
2025-08-13 16:51:50,042 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:81] - 账户是否锁定: false
2025-08-13 16:51:50,042 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:82] - 账户锁定时间: null
2025-08-13 16:51:50,042 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:89] - 密码是否过期: false
2025-08-13 16:51:50,042 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:90] - 密码更新时间: 2025-08-12T14:03:06
2025-08-13 16:51:50,042 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:96] - === 加载用户权限 ===
2025-08-13 16:51:50,047 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:101] - 用户权限列表: [ROLE_ADMIN]
2025-08-13 16:51:50,047 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:102] - 权限数量: 1
2025-08-13 16:51:50,048 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:105] - === 构建UserDetails对象 ===
2025-08-13 16:51:50,048 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:106] - 用户名: admin
2025-08-13 16:51:50,048 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:107] - 密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 16:51:50,048 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:108] - 账户过期: false
2025-08-13 16:51:50,048 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:109] - 账户锁定: false
2025-08-13 16:51:50,048 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:110] - 凭据过期: false
2025-08-13 16:51:50,048 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:111] - 账户禁用: false
2025-08-13 16:51:50,049 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:123] - ✅ 用户 ad***n 认证信息加载完成
2025-08-13 16:51:50,050 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-13 16:51:50,050 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-13 16:51:50,050 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: admin123
2025-08-13 16:51:50,050 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 16:51:50,050 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-13 16:51:50,050 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2a$10$FxZ
2025-08-13 16:51:50,050 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-13 16:51:50,050 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-13 16:51:50,115 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: true
2025-08-13 16:51:50,115 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:109] - ✅ 密码验证成功
2025-08-13 16:51:50,115 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:45] - ✅ 认证成功: admin
2025-08-13 16:51:50,115 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:46] - 认证结果权限: [ROLE_ADMIN]
2025-08-13 16:52:33,791 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-13 16:52:33,792 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-13 16:52:33,792 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 8
2025-08-13 16:52:33,793 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: admin123
2025-08-13 16:52:33,802 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:71] - === 检查用户状态 ===
2025-08-13 16:52:33,802 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:73] - 用户是否启用: true
2025-08-13 16:52:33,802 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:81] - 账户是否锁定: false
2025-08-13 16:52:33,802 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:82] - 账户锁定时间: null
2025-08-13 16:52:33,802 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:89] - 密码是否过期: false
2025-08-13 16:52:33,802 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:90] - 密码更新时间: 2025-08-12T14:03:06
2025-08-13 16:52:33,803 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:96] - === 加载用户权限 ===
2025-08-13 16:52:33,806 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:101] - 用户权限列表: [ROLE_ADMIN]
2025-08-13 16:52:33,806 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:102] - 权限数量: 1
2025-08-13 16:52:33,806 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:105] - === 构建UserDetails对象 ===
2025-08-13 16:52:33,806 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:106] - 用户名: admin
2025-08-13 16:52:33,807 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:107] - 密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 16:52:33,807 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:108] - 账户过期: false
2025-08-13 16:52:33,807 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:109] - 账户锁定: false
2025-08-13 16:52:33,807 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:110] - 凭据过期: false
2025-08-13 16:52:33,807 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:111] - 账户禁用: false
2025-08-13 16:52:33,807 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.DatabaseUserDetailsService [L:123] - ✅ 用户 ad***n 认证信息加载完成
2025-08-13 16:52:33,807 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-13 16:52:33,808 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-13 16:52:33,808 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: admin123
2025-08-13 16:52:33,808 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 16:52:33,808 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-13 16:52:33,808 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2a$10$FxZ
2025-08-13 16:52:33,808 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-13 16:52:33,809 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-13 16:52:33,894 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: true
2025-08-13 16:52:33,894 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:109] - ✅ 密码验证成功
2025-08-13 16:52:33,894 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:45] - ✅ 认证成功: admin
2025-08-13 16:52:33,894 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.LoggingAuthenticationProvider [L:46] - 认证结果权限: [ROLE_ADMIN]
2025-08-13 16:53:29,564 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-13 16:53:29,566 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-13 16:53:29,567 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 8
2025-08-13 16:53:29,567 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: admin123
2025-08-13 16:53:29,577 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:71] - === 检查用户状态 ===
2025-08-13 16:53:29,577 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:73] - 用户是否启用: true
2025-08-13 16:53:29,578 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:81] - 账户是否锁定: false
2025-08-13 16:53:29,578 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:82] - 账户锁定时间: null
2025-08-13 16:53:29,578 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:89] - 密码是否过期: false
2025-08-13 16:53:29,578 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:90] - 密码更新时间: 2025-08-12T14:03:06
2025-08-13 16:53:29,578 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:96] - === 加载用户权限 ===
2025-08-13 16:53:29,582 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:101] - 用户权限列表: [ROLE_ADMIN]
2025-08-13 16:53:29,582 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:102] - 权限数量: 1
2025-08-13 16:53:29,582 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:105] - === 构建UserDetails对象 ===
2025-08-13 16:53:29,582 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:106] - 用户名: admin
2025-08-13 16:53:29,583 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:107] - 密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 16:53:29,583 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:108] - 账户过期: false
2025-08-13 16:53:29,583 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:109] - 账户锁定: false
2025-08-13 16:53:29,583 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:110] - 凭据过期: false
2025-08-13 16:53:29,583 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:111] - 账户禁用: false
2025-08-13 16:53:29,583 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:123] - ✅ 用户 ad***n 认证信息加载完成
2025-08-13 16:53:29,583 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-13 16:53:29,583 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-13 16:53:29,584 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: admin123
2025-08-13 16:53:29,584 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 16:53:29,584 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-13 16:53:29,584 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2a$10$FxZ
2025-08-13 16:53:29,584 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-13 16:53:29,584 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-13 16:53:29,666 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: true
2025-08-13 16:53:29,666 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:109] - ✅ 密码验证成功
2025-08-13 16:53:29,667 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:45] - ✅ 认证成功: admin
2025-08-13 16:53:29,667 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.LoggingAuthenticationProvider [L:46] - 认证结果权限: [ROLE_ADMIN]
2025-08-13 16:55:53,927 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-13 16:55:53,928 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-13 16:55:53,929 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 8
2025-08-13 16:55:53,929 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: admin123
2025-08-13 16:55:53,936 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:71] - === 检查用户状态 ===
2025-08-13 16:55:53,936 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:73] - 用户是否启用: true
2025-08-13 16:55:53,937 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:81] - 账户是否锁定: false
2025-08-13 16:55:53,937 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:82] - 账户锁定时间: null
2025-08-13 16:55:53,937 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:89] - 密码是否过期: false
2025-08-13 16:55:53,937 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:90] - 密码更新时间: 2025-08-12T14:03:06
2025-08-13 16:55:53,937 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:96] - === 加载用户权限 ===
2025-08-13 16:55:53,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:101] - 用户权限列表: [ROLE_ADMIN]
2025-08-13 16:55:53,945 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:102] - 权限数量: 1
2025-08-13 16:55:53,945 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:105] - === 构建UserDetails对象 ===
2025-08-13 16:55:53,945 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:106] - 用户名: admin
2025-08-13 16:55:53,945 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:107] - 密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 16:55:53,945 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:108] - 账户过期: false
2025-08-13 16:55:53,945 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:109] - 账户锁定: false
2025-08-13 16:55:53,945 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:110] - 凭据过期: false
2025-08-13 16:55:53,945 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:111] - 账户禁用: false
2025-08-13 16:55:53,945 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:123] - ✅ 用户 ad***n 认证信息加载完成
2025-08-13 16:55:53,946 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-13 16:55:53,946 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-13 16:55:53,946 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: admin123
2025-08-13 16:55:53,946 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 16:55:53,946 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-13 16:55:53,946 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2a$10$FxZ
2025-08-13 16:55:53,946 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-13 16:55:53,946 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-13 16:55:54,031 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: true
2025-08-13 16:55:54,032 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:109] - ✅ 密码验证成功
2025-08-13 16:55:54,032 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:45] - ✅ 认证成功: admin
2025-08-13 16:55:54,032 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.LoggingAuthenticationProvider [L:46] - 认证结果权限: [ROLE_ADMIN]
2025-08-13 16:56:36,337 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 16:56:36,359 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-13 16:56:36,361 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
