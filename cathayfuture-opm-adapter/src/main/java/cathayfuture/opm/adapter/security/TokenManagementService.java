package cathayfuture.opm.adapter.security;

import com.dtyunxi.huieryun.cache.api.IRedisCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Token管理服务
 * 负责JWT token的Redis存储、验证和失效管理
 * 
 * <AUTHOR>
 * @since 2025-08-12
 */
@Slf4j
@Service
public class TokenManagementService {
    
    private static final String TOKEN_CACHE_GROUP = "JWT_TOKEN_CACHE";
    private static final String REFRESH_TOKEN_CACHE_GROUP = "JWT_REFRESH_TOKEN_CACHE";
    private static final String USER_TOKEN_MAPPING_GROUP = "USER_TOKEN_MAPPING";
    private static final String USER_LOGIN_LOCK_GROUP = "USER_LOGIN_LOCK";
    
    // Access Token有效期：1天（与JWT中的过期时间保持一致）
    private static final int ACCESS_TOKEN_EXPIRE_HOURS = 1 * 24;
    // Refresh Token有效期：2天
    private static final int REFRESH_TOKEN_EXPIRE_HOURS = 2 * 24;
    
    @Resource
    private IRedisCacheService cacheService;
    
    /**
     * 是否启用单用户单Token策略（默认启用）
     * true: 新登录会使旧token失效（推荐）
     * false: 允许同一用户多个有效token（不推荐）
     */
    @Value("${cathay.security.token.single-token-per-user:true}")
    private boolean singleTokenPerUser;
    
    /**
     * 存储token到Redis
     * @param token JWT token
     * @param username 用户名
     * @param userInfo 用户信息JSON字符串
     * @return 是否存储成功
     */
    public boolean storeToken(String token, String username, String userInfo) {
        // 使用用户名作为锁，防止并发登录时的竞态条件
        String lockKey = "user_login_lock:" + username;
        String lockValue = String.valueOf(System.currentTimeMillis()); // 使用时间戳作为锁值

        try {
            // 1. 尝试获取分布式锁（使用SET NX EX实现原子操作）
            // 尝试设置锁，如果key不存在则设置成功，存在则失败（原子操作）
            boolean lockAcquired = false;
            int maxRetries = 3;
            int retryCount = 0;

            while (!lockAcquired && retryCount < maxRetries) {
                try {
                    // 检查是否已有锁存在
                    String existingLock = cacheService.getCache(USER_LOGIN_LOCK_GROUP, lockKey, String.class);
                    if (existingLock == null) {
                        // 尝试设置锁（30秒超时，足够完成登录流程）
                        cacheService.setCache(USER_LOGIN_LOCK_GROUP, lockKey, lockValue, 30);
                        // 再次验证锁是否设置成功（防止竞态条件）
                        String verifyLock = cacheService.getCache(USER_LOGIN_LOCK_GROUP, lockKey, String.class);
                        if (lockValue.equals(verifyLock)) {
                            lockAcquired = true;
                            log.debug("获取登录锁成功: username={}, lockValue={}", username, lockValue);
                        }
                    }

                    if (!lockAcquired) {
                        retryCount++;
                        if (retryCount < maxRetries) {
                            Thread.sleep(100); // 等待100ms后重试
                        }
                    }
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("获取登录锁被中断: username={}", username);
                    return false;
                }
            }

            if (!lockAcquired) {
                log.warn("用户 {} 正在登录中，请稍后重试（已重试{}次）", username, maxRetries);
                return false;
            }

            try {
                // 2. 如果启用单用户单token策略，清理用户的旧token
                if (singleTokenPerUser) {
                    invalidateUserTokensSilently(username);
                    log.debug("单用户单Token策略：已清理用户 {} 的旧token", username);
                }

                // 3. 存储新的token信息
                TokenInfo tokenInfo = new TokenInfo();
                tokenInfo.setToken(token);
                tokenInfo.setUsername(username);
                tokenInfo.setUserInfo(userInfo);
                tokenInfo.setCreateTime(System.currentTimeMillis());
                tokenInfo.setLastAccessTime(System.currentTimeMillis());

                // 4. 将token存储到Redis，设置过期时间（Redis自动过期机制）
                cacheService.setCache(TOKEN_CACHE_GROUP, token, tokenInfo, ACCESS_TOKEN_EXPIRE_HOURS * 3600);

                // 5. 建立用户名到token的映射（用于登出和单用户限制）
                cacheService.setCache(USER_TOKEN_MAPPING_GROUP, username, token, ACCESS_TOKEN_EXPIRE_HOURS * 3600);

                log.debug("Token存储成功: username={}, token前缀={}, 单token策略={}",
                    username, token.substring(0, Math.min(20, token.length())), singleTokenPerUser);
                return true;
            } finally {
                // 6. 释放分布式锁（只有锁的拥有者才能释放）
                releaseLockSafely(lockKey, lockValue, username);
            }
        } catch (Exception e) {
            log.error("Token存储失败: username={}, error={}", username, e.getMessage(), e);
            // 确保锁被释放
            releaseLockSafely(lockKey, lockValue, username);
            return false;
        }
    }

    /**
     * 安全释放分布式锁
     * @param lockKey 锁的key
     * @param lockValue 锁的值
     * @param username 用户名（用于日志）
     */
    private void releaseLockSafely(String lockKey, String lockValue, String username) {
        try {
            // 只有锁的拥有者才能释放锁
            String currentLockValue = cacheService.getCache(USER_LOGIN_LOCK_GROUP, lockKey, String.class);
            if (lockValue.equals(currentLockValue)) {
                cacheService.delCache(USER_LOGIN_LOCK_GROUP, lockKey);
                log.debug("释放登录锁成功: username={}, lockValue={}", username, lockValue);
            } else {
                log.debug("锁已被其他进程释放或过期: username={}, expectedValue={}, actualValue={}",
                    username, lockValue, currentLockValue);
            }
        } catch (Exception lockEx) {
            log.error("释放登录锁失败: username={}, lockValue={}, error={}", username, lockValue, lockEx.getMessage());
        }
    }
    
    /**
     * 验证token是否有效
     * @param token JWT token
     * @return 是否有效
     */
    public boolean isTokenValid(String token) {
        if (!StringUtils.hasText(token)) {
            return false;
        }
        
        try {
            TokenInfo tokenInfo = cacheService.getCache(TOKEN_CACHE_GROUP, token, TokenInfo.class);
            if (tokenInfo == null) {
                log.debug("Token在Redis中不存在: {}", token.substring(0, Math.min(20, token.length())));
                return false;
            }
            
            // 更新最后访问时间
            tokenInfo.setLastAccessTime(System.currentTimeMillis());
            cacheService.setCache(TOKEN_CACHE_GROUP, token, tokenInfo, ACCESS_TOKEN_EXPIRE_HOURS * 3600);
            
            log.debug("Token验证成功: username={}", tokenInfo.getUsername());
            return true;
        } catch (Exception e) {
            log.error("Token验证失败: error={}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取token信息
     * @param token JWT token
     * @return token信息
     */
    public TokenInfo getTokenInfo(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }
        
        try {
            return cacheService.getCache(TOKEN_CACHE_GROUP, token, TokenInfo.class);
        } catch (Exception e) {
            log.error("获取Token信息失败: error={}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 使token失效（登出）
     * @param token JWT token
     * @return 是否成功
     */
    public boolean invalidateToken(String token) {
        if (!StringUtils.hasText(token)) {
            return false;
        }
        
        try {
            // 1. 获取token信息
            TokenInfo tokenInfo = cacheService.getCache(TOKEN_CACHE_GROUP, token, TokenInfo.class);
            if (tokenInfo != null) {
                // 2. 删除用户名到token的映射
                cacheService.delCache(USER_TOKEN_MAPPING_GROUP, tokenInfo.getUsername());
                log.debug("删除用户token映射: username={}", tokenInfo.getUsername());
            }
            
            // 3. 删除token
            cacheService.delCache(TOKEN_CACHE_GROUP, token);
            
            log.info("Token失效成功: token前缀={}", token.substring(0, Math.min(20, token.length())));
            return true;
        } catch (Exception e) {
            log.error("Token失效失败: error={}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 使用户的所有token失效（强制登出）
     * @param username 用户名
     * @return 是否成功
     */
    public boolean invalidateUserTokens(String username) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        
        try {
            // 1. 获取用户当前的token
            String currentToken = cacheService.getCache(USER_TOKEN_MAPPING_GROUP, username, String.class);
            if (StringUtils.hasText(currentToken)) {
                // 2. 删除token
                cacheService.delCache(TOKEN_CACHE_GROUP, currentToken);
                log.debug("删除用户token: username={}, token前缀={}", username, currentToken.substring(0, Math.min(20, currentToken.length())));
            }
            
            // 3. 删除用户名到token的映射
            cacheService.delCache(USER_TOKEN_MAPPING_GROUP, username);
            
            log.info("用户所有Token失效成功: username={}", username);
            return true;
        } catch (Exception e) {
            log.error("用户Token失效失败: username={}, error={}", username, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 静默使用户的所有token失效（不记录INFO日志）
     * 用于单用户单token策略的内部清理
     * @param username 用户名
     * @return 是否成功
     */
    private boolean invalidateUserTokensSilently(String username) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        
        try {
            // 1. 获取用户当前的token
            String currentToken = cacheService.getCache(USER_TOKEN_MAPPING_GROUP, username, String.class);
            if (StringUtils.hasText(currentToken)) {
                // 2. 删除token
                cacheService.delCache(TOKEN_CACHE_GROUP, currentToken);
                log.debug("静默删除用户token: username={}, token前缀={}", username, currentToken.substring(0, Math.min(20, currentToken.length())));
            }
            
            // 3. 删除用户名到token的映射
            cacheService.delCache(USER_TOKEN_MAPPING_GROUP, username);
            
            return true;
        } catch (Exception e) {
            log.debug("静默清理用户Token失败: username={}, error={}", username, e.getMessage());
            return false;
        }
    }
    
    /**
     * 生成refresh token（使用JWT格式）
     * @param username 用户名
     * @return refresh token
     */
    public String generateRefreshToken(String username) {
        try {
            // 创建refresh token的payload
            Map<String, Object> payload = new HashMap<>();
            payload.put("username", username);
            payload.put("type", BmsJwtUtils.BMS_TOKEN_TYPE_REFRESH);
            
            // 使用BmsJwtUtils生成JWT格式的refresh token
            String refreshToken = BmsJwtUtils.createBmsRefreshToken(payload);
            
            if (refreshToken == null) {
                log.error("JWT Refresh Token生成失败: username={}", username);
                return null;
            }
            
            // 存储refresh token信息到Redis，设置2天过期
            RefreshTokenInfo refreshTokenInfo = new RefreshTokenInfo();
            refreshTokenInfo.setRefreshToken(refreshToken);
            refreshTokenInfo.setUsername(username);
            refreshTokenInfo.setCreateTime(System.currentTimeMillis());
            
            cacheService.setCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken, refreshTokenInfo, REFRESH_TOKEN_EXPIRE_HOURS * 3600);
            
            log.debug("JWT Refresh Token生成成功: username={}", username);
            return refreshToken;
        } catch (Exception e) {
            log.error("JWT Refresh Token生成失败: username={}, error={}", username, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 验证refresh token（JWT格式）
     * @param refreshToken refresh token
     * @return 用户名，如果无效返回null
     */
    public String validateRefreshToken(String refreshToken) {
        if (!StringUtils.hasText(refreshToken)) {
            return null;
        }
        
        try {
            // 1. 首先验证JWT签名和过期时间
            if (!BmsJwtUtils.verifyBmsRefreshToken(refreshToken)) {
                log.debug("BMS Refresh Token JWT验证失败");
                return null;
            }
            
            // 2. 验证token是否在Redis中存在（未被注销）
            RefreshTokenInfo refreshTokenInfo = cacheService.getCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken, RefreshTokenInfo.class);
            if (refreshTokenInfo != null) {
                // 3. 从JWT中提取用户名进行双重验证
                String usernameFromJWT = BmsJwtUtils.getUsernameFromBmsRefreshToken(refreshToken);
                if (StringUtils.hasText(usernameFromJWT)) {
                    log.debug("BMS JWT Refresh Token验证成功: username={}", refreshTokenInfo.getUsername());
                    return refreshTokenInfo.getUsername();
                } else {
                    log.debug("从BMS Refresh Token中无法提取用户信息");
                    return null;
                }
            }
            
            log.debug("Refresh Token在Redis中不存在或已过期");
            return null;
        } catch (Exception e) {
            log.error("JWT Refresh Token验证失败: error={}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 使refresh token失效
     * @param refreshToken refresh token
     * @return 是否成功
     */
    public boolean invalidateRefreshToken(String refreshToken) {
        if (!StringUtils.hasText(refreshToken)) {
            return false;
        }
        
        try {
            cacheService.delCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken);
            log.debug("Refresh Token失效成功");
            return true;
        } catch (Exception e) {
            log.error("Refresh Token失效失败: error={}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Token信息内部类
     */
    public static class TokenInfo {
        private String token;
        private String username;
        private String userInfo;
        private long createTime;
        private long lastAccessTime;

        // Getters and Setters
        public String getToken() { return token; }
        public void setToken(String token) { this.token = token; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getUserInfo() { return userInfo; }
        public void setUserInfo(String userInfo) { this.userInfo = userInfo; }

        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }

        public long getLastAccessTime() { return lastAccessTime; }
        public void setLastAccessTime(long lastAccessTime) { this.lastAccessTime = lastAccessTime; }
    }
    
    /**
     * Refresh Token信息内部类
     */
    public static class RefreshTokenInfo {
        private String refreshToken;
        private String username;
        private long createTime;
        
        // Getters and Setters
        public String getRefreshToken() { return refreshToken; }
        public void setRefreshToken(String refreshToken) { this.refreshToken = refreshToken; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }
    }
}
