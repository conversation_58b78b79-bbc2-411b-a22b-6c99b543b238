management.health.redis.enabled: false
spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: *****************************************************************************************************************************************************************************************
    username: root
    password: Vx8SRE`k
  main:
    allow-bean-definition-overriding: true

  jackson:
    serialization:
      write-dates-as-timestamps: true
    deserialization:
      fail_on_unknown_properties: false

huieryun.cacheregistryvo:
  port: 63791
  appId: ""
  livetime: 86370
  host: *********
  appSecret: f3l+Upvb
  type: REDIS

huieryun.service.lock.lockregistryvo:
  provider: redis
  endpoints: *********:63791
  passwd: f3l+Upvb

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

wx:
  miniapp:
    configs:
      - appid: wx3f126b1c4bc46dcf
        secret: 0eebded2c699174c6c30953541774d79
        token:
        aesKey:
        msgDataFormat: JSON

payment-center:
  user-no: CATHAYFUTURE
  pay-product-code: CATHAYFUTURE_PAYMENT
  pay-type-code: WX_JSAPI
  pay-way-code: WX
  pre-pay-url: https://mpgateway-dev.tasly.com/pay-center/payment/pre-order
  notify-url: https://mpgateway-dev.tasly.com/api/cathayfuture/mobile/callbacks/pay/notify
  query-pay-result-url: https://mpgateway-dev.tasly.com/pay-center/payment/wx/query-order/{tenantNo}/{tenantOrderNo}

huieryun.ossregistryvo:
  ossType: AmazonS3
  endpoint: https://b2b.i.tasly.com
  accessKeyId: IGIyYmRldjAx
  accessKeySecret: b047853fce4e4286bb5004d52c631928
  acl: public-read
  extFields: {ossTenant: b2b, anonUrl: https://api.i.tasly.com/swift/v1}
  bucketName: middle-platform-public-dev
  extProperty: {middle-platform-public-dev: middle-platform-public-dev}
  dir: tasly-center-rebate/
huieryun.ossappvo:
  appId: tasly

sms:
  ali:
    accessKeyId: LTAI4FzjFk5UNDpRZH3AUKbh
    accessKeySecret: ******************************
    signName: 天人智慧
    templateCode: SMS_170775265
    templateParamName: code
  send:
    profiles: prd,prod,test,uat

order:
  code:
    prefix: CF_TEST
  frontMoney:
    amount: 0.01

# 固定用户配置
cathay:
  security:
    dev-mode: false
    use-database-auth: true  # 启用数据库认证
    # Token管理策略
    token:
      # 单用户单Token策略（推荐启用以防止Token累积）
      single-token-per-user: ${SINGLE_TOKEN_PER_USER:true}
    jwt:
      bms:
        # JWT密钥（生产环境必须通过环境变量或配置中心设置）
        secret: ${BMS_JWT_SECRET:Q0FUSEFZLUZVVFVSRS1CTVMtU1lTVEVNLVNFQ1VSSVRZPQ==}
        # Access Token有效期（小时）
        access-token-hours: ${BMS_JWT_ACCESS_TOKEN_HOURS:24}
        # Refresh Token有效期（小时）
        refresh-token-hours: ${BMS_JWT_REFRESH_TOKEN_HOURS:48}
        # JWT发行人
        issuer: ${BMS_JWT_ISSUER:CATHAY-FUTURE-BMS-SYSTEM}
        # JWT受众
        audience: ${BMS_JWT_AUDIENCE:CATHAY-FUTURE-BMS-SYSTEM}
    # 登录速率限制配置
    rate-limit:
      ip:
        # 每个IP每分钟最多尝试次数
        max-attempts: ${LOGIN_RATE_LIMIT_IP_MAX_ATTEMPTS:100}
        # IP限制时间窗口（分钟）
        window-minutes: ${LOGIN_RATE_LIMIT_IP_WINDOW_MINUTES:1}
        # IP锁定时间（分钟）
        lockout-minutes: ${LOGIN_RATE_LIMIT_IP_LOCKOUT_MINUTES:5}
      username:
        # 每个用户名每小时最多尝试次数
        max-attempts: ${LOGIN_RATE_LIMIT_USERNAME_MAX_ATTEMPTS:50}
        # 用户名限制时间窗口（小时）
        window-hours: ${LOGIN_RATE_LIMIT_USERNAME_WINDOW_HOURS:1}
        # 用户名锁定时间（小时）
        lockout-hours: ${LOGIN_RATE_LIMIT_USERNAME_LOCKOUT_HOURS:1}
  auth:
    # 新格式：多用户支持
    users:
      - username: admin
        password: admin123
        role: ROLE_ADMIN
        user-id: 1
        role-ids: "1,2,3"
        user-name: "系统管理员"
        post-code: "ADMIN"
        post-name: "超级管理员"
      - username: manager
        password: manager123
        role: ROLE_MANAGER
        user-id: 2
        role-ids: "2,3"
        user-name: "业务管理员"
        post-code: "MANAGER"
        post-name: "业务管理员"
    # 旧格式：向后兼容（默认使用admin用户）
    username: admin
    password: admin123
    user-id: 1
    role-ids: "1,2,3"
    user-name: "系统管理员"
    post-code: "ADMIN"
    post-name: "超级管理员"